export interface ChatbotStatusResponse {
	message: string;
	uid: string;
	email: string;
	has_groq_api_key: boolean;
}

export interface AddGrokKeyRequest {
	groq_api_key: string;
}

export interface AddGrokKeyResponse {
	success: boolean;
	message: string;
}

export interface MCQChatbotRequest {
	mcqid: string;
	mcqTitle: string;
	options: string[];
	userChoice: string;
	correctAnswer: string;
	explanation: string;
	question: string;
}

export interface MCQChatbotResponse {
	response: string;
}
