import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import { useMediaQuery } from "react-responsive";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, X } from "react-feather";
import YouTube, { YouTubeProps } from "react-youtube";
import { extractYouTubeVideoId } from "@/lib/utils";
import { useGrokKeyStatus, useMCQChatbot } from "@/lib/queries/chatbot.query";
import GrokKeyBanner from "@/components/chatbot/grok-key-banner";
import { useToast } from "@/hooks/use-toast";
import MobileHeader from "@/components/common/mobile-header";

// Mock resource data - in real implementation, this would come from API
const mockResource = {
	id: "1",
	title: "The best French lesson are with <PERSON><PERSON><PERSON>",
	description:
		"Unlock the power of Figma, the leading collaborative design tool, with our comprehensive online course. Whether you're a novice or looking to enhance your skills, this course will guide you through Figma's robust features and workflows.\n\nPerfect for UI/UX designers, product managers, and anyone interested in modern design tools. Join us to elevate your design skills and boost your productivity with Figma!\n\nWhat You'll Learn:\n• Setting up the environment\n• Advanced HTML practices\n• Build a portfolio website\n• Understanding HTML Programming\n• Code HTML\n• Start building beautiful websites",
	url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", // Sample YouTube URL
	subject: ["French"],
	type: "video",
	chapter: ["Basics"],
	topic: "Introduction",
};

const VideoResourcePage = () => {
	// const { resourceId } = Route.useParams();
	const { toast } = useToast();
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// Chatbot states
	const [showChatbot, setShowChatbot] = useState(false);
	const [chatbotQuestion, setChatbotQuestion] = useState("");
	const [chatbotResponse, setChatbotResponse] = useState("");

	// Queries
	const { data: grokKeyStatus } = useGrokKeyStatus();
	const chatbotMutation = useMCQChatbot();

	// YouTube player options
	const youtubeOpts: YouTubeProps["opts"] = {
		width: "100%",
		height: isDesktop ? "500" : "250",
		playerVars: {
			autoplay: 0,
		},
	};

	// Handle chatbot submission with dummy MCQ data
	const handleChatbotSubmit = async () => {
		if (!chatbotQuestion.trim() || !grokKeyStatus?.has_groq_api_key) return;

		// Create dummy MCQ-based payload for video resource
		const payload = {
			mcqid: mockResource.id,
			mcqTitle: mockResource.title,
			options: ["A. Option 1", "B. Option 2", "C. Option 3", "D. Option 4"],
			userChoice: "A. Option 1",
			correctAnswer: "B. Option 2",
			explanation: mockResource.description,
			question: chatbotQuestion,
		};

		try {
			const response = await chatbotMutation.mutateAsync(payload);
			setChatbotResponse(response.data.response);
			setChatbotQuestion("");
		} catch (error: any) {
			toast({
				title: "Error",
				description:
					error?.response?.data?.message || "Failed to get chatbot response",
				variant: "destructive",
			});
		}
	};

	if (isDesktop) {
		return (
			<div className="min-h-screen bg-gray-50">
				<div className="container mx-auto px-4 py-6">
					<div className="grid grid-cols-1 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
						{/* Main Content - Video and Tabs */}
						<div className="lg:col-span-2 2xl:col-span-3">
							{/* Video Player */}
							<div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
								<div className="aspect-video">
									<YouTube
										videoId={extractYouTubeVideoId(mockResource.url)}
										opts={youtubeOpts}
										className="w-full h-full"
									/>
								</div>
							</div>

							{/* Tabs */}
							<Tabs defaultValue="overview" className="w-full">
								<TabsList className="grid w-full grid-cols-1">
									<TabsTrigger
										value="overview"
										className="data-[state=active]:bg-purple-600 data-[state=active]:text-white"
									>
										Overview
									</TabsTrigger>
								</TabsList>
								<TabsContent value="overview" className="mt-4">
									<div className="bg-white rounded-lg p-6 shadow-sm">
										<h2 className="text-xl font-semibold mb-4">About Course</h2>
										<p className="text-gray-600 whitespace-pre-line">
											{mockResource.description}
										</p>
									</div>
								</TabsContent>
							</Tabs>
						</div>

						{/* Sidebar - Chatbot */}
						<div className="lg:col-span-1">
							<div className="bg-white rounded-lg shadow-sm p-4 sticky top-6 max-h-[calc(100vh-2rem)] overflow-y-auto">
								{!showChatbot ? (
									/* Ask Chatbot Button */
									<div className="mb-4">
										<Button
											variant="outline"
											onClick={() => setShowChatbot(true)}
											className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50 w-full justify-center"
										>
											<span className="text-lg">🤖</span>
											Ask Chatbot
										</Button>
									</div>
								) : (
									/* Full Chatbot Interface */
									<div className="flex flex-col justify-between space-y-4 h-[500px]">
										{/* Header with close button */}
										<div>
											<div className="flex items-center justify-between mb-4">
												<div className="flex items-center gap-2">
													<div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
														<span className="text-white text-sm">🤖</span>
													</div>
													<h3 className="font-semibold">AI Chatbot</h3>
												</div>
												<Button
													variant="icon"
													size="sm"
													onClick={() => setShowChatbot(false)}
												>
													<X size={16} />
												</Button>
											</div>
											{/* Grok Key Banner */}
											{!chatbotResponse && (
												<GrokKeyBanner
													hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
													className="mb-4"
												/>
											)}
										</div>

										{/* Start a Chat section */}
										{!chatbotResponse && (
											<div className="text-center mb-6">
												<h4 className="font-semibold text-lg mb-2">
													Start a Chat!
												</h4>
												<p className="text-sm text-gray-600">
													Ask AI questions about the specific question or topic.
												</p>
											</div>
										)}

										{/* Chatbot Response */}
										{chatbotResponse && (
											<div className="bg-gray-50 rounded-lg p-3 mb-4">
												<div className="flex items-start gap-2">
													<span className="text-lg">🤖</span>
													<div>
														<p className="text-sm font-medium mb-1">
															Chatbot says...
														</p>
														<p className="text-sm text-gray-700 whitespace-pre-wrap">
															{chatbotResponse}
														</p>
													</div>
												</div>
											</div>
										)}

										{/* Chat Input */}
										<div className="flex gap-2">
											<Input
												placeholder="Write a message!"
												value={chatbotQuestion}
												onChange={(e) => setChatbotQuestion(e.target.value)}
												disabled={
													chatbotMutation.isPending ||
													!grokKeyStatus?.has_groq_api_key
												}
												onKeyDown={(e) => {
													if (
														e.key === "Enter" &&
														grokKeyStatus?.has_groq_api_key
													) {
														handleChatbotSubmit();
													}
												}}
											/>
											<Button
												onClick={handleChatbotSubmit}
												disabled={
													chatbotMutation.isPending ||
													!chatbotQuestion.trim() ||
													!grokKeyStatus?.has_groq_api_key
												}
												className="bg-purple-600 hover:bg-purple-700"
											>
												{chatbotMutation.isPending ? (
													<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
												) : (
													<Send size={16} />
												)}
											</Button>
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Mobile Layout
	return (
		<div className="min-h-screen bg-white">
			<MobileHeader resourceType="video" />
			<div className="pt-[80px]">
				{/* Video Player */}
				<div className="bg-white">
					<YouTube
						videoId={extractYouTubeVideoId(mockResource.url)}
						opts={youtubeOpts}
						className="w-full"
					/>
				</div>

				{/* Title and Subtitle */}
				<div className="p-4 border-b">
					<h1 className="text-lg font-semibold mb-1">{mockResource.title}</h1>
					<p className="text-sm text-gray-600">Subtitle</p>
				</div>

				{/* Tabs */}
				<Tabs defaultValue="chatbot" className="w-full">
					<TabsList className="grid w-full grid-cols-2 mx-4 mt-4">
						<TabsTrigger
							value="chatbot"
							className="data-[state=active]:bg-purple-600 data-[state=active]:text-white"
						>
							AI Chatbot
						</TabsTrigger>
						<TabsTrigger
							value="overview"
							className="data-[state=active]:bg-purple-600 data-[state=active]:text-white"
						>
							Overview
						</TabsTrigger>
					</TabsList>

					<TabsContent value="chatbot" className="p-4">
						<div className="space-y-4">
							<div className="text-center">
								<h3 className="text-lg font-semibold mb-2">Start a Chat!</h3>
								<p className="text-sm text-gray-600 mb-4">
									Ask AI questions about the specific question or topic.
								</p>
							</div>

							{/* Grok Key Banner */}
							{!chatbotResponse && (
								<GrokKeyBanner
									hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
									className="mb-4"
								/>
							)}

							{/* Chatbot Response */}
							{chatbotResponse && (
								<div className="bg-gray-50 rounded-lg p-3 mb-4">
									<div className="flex items-start gap-2">
										<span className="text-lg">🤖</span>
										<div>
											<p className="text-sm font-medium mb-1">
												Chatbot says...
											</p>
											<p className="text-sm text-gray-700 whitespace-pre-wrap">
												{chatbotResponse}
											</p>
										</div>
									</div>
								</div>
							)}

							{/* Chat Input */}
							<div className="flex gap-2">
								<Input
									placeholder="Write a message!"
									value={chatbotQuestion}
									onChange={(e) => setChatbotQuestion(e.target.value)}
									disabled={
										chatbotMutation.isPending ||
										!grokKeyStatus?.has_groq_api_key
									}
									onKeyDown={(e) => {
										if (e.key === "Enter" && grokKeyStatus?.has_groq_api_key) {
											handleChatbotSubmit();
										}
									}}
								/>
								<Button
									onClick={handleChatbotSubmit}
									disabled={
										chatbotMutation.isPending ||
										!chatbotQuestion.trim() ||
										!grokKeyStatus?.has_groq_api_key
									}
									className="bg-purple-600 hover:bg-purple-700"
								>
									{chatbotMutation.isPending ? (
										<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
									) : (
										<Send size={16} />
									)}
								</Button>
							</div>
						</div>
					</TabsContent>

					<TabsContent value="overview" className="p-4">
						<div className="space-y-4">
							<h2 className="text-lg font-semibold">About Course</h2>
							<p className="text-gray-600 whitespace-pre-line">
								{mockResource.description}
							</p>
						</div>
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/resource/$resourceId")(
	{
		component: VideoResourcePage,
	}
);
